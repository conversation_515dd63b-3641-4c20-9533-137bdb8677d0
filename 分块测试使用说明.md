# 向量化分块测试脚本使用说明

## 脚本功能
`test_chunking_script.py` 是一个专门用于测试向量化分块效果的脚本，支持：
- 真实CSV/Excel文件测试
- 详细的分块结果分析
- 可视化的分布统计
- 示例块内容展示
- 交互式和命令行两种模式

## 使用方法

### 1. 命令行模式
```bash
# 测试指定文件
python test_chunking_script.py /path/to/your/file.csv

# 测试文件但不显示示例内容
python test_chunking_script.py /path/to/your/file.csv --no-samples

# 自定义显示的示例块数量
python test_chunking_script.py /path/to/your/file.csv --samples 5
```

### 2. 交互模式
```bash
# 启动交互模式
python test_chunking_script.py

# 然后按提示输入文件路径
请输入文件路径: /Users/<USER>/Desktop/risk_data.csv
```

## 输出说明

### 基本统计
- **总块数**: 生成的文档块总数
- **块大小范围**: 最小和最大块的字符数
- **平均块大小**: 所有块的平均字符数
- **每块行数范围**: 最少和最多的行数
- **平均每块行数**: 所有块的平均行数

### 分布分析
#### 块大小分布
```
📊 块大小分布:
   2KB-3KB: 146 个块 (89.0%) ████████████████████
   3KB-4KB:  18 个块 (11.0%) ██
```

#### 行数分布
```
📋 行数分布:
    6 行: 146 个块 (89.0%) ████████████████████
    7 行:  18 个块 (11.0%) ██
```

### 质量评估
- **目标范围**: 配置的合理块大小范围
- **在范围内**: 符合要求的块数量和百分比
- **行数多样性**: 不同行数值的种类数
- **处理行数**: 实际处理的总行数

### 示例块内容
显示前几个块的详细信息：
- 块ID和行范围
- 行数和字符数
- 内容预览（前300字符）

## 支持的文件格式
- CSV文件 (`.csv`)
- Excel文件 (`.xlsx`, `.xls`)

## 配置参数
脚本会显示当前的分块配置：
```python
CHUNK_CONFIG = {
    "target_chars_per_chunk": 3072,  # 目标块大小
    "min_chunk_size_chars": 2048,    # 最小块大小
    "max_chunk_size_chars": 5120,    # 最大块大小
    "absolute_max_chars": 8192,      # 绝对上限
    "min_rows_per_chunk": 5,         # 最少行数
    "max_total_chunks": 200,         # 最大块数
    "max_stats_columns": 3,          # 统计列数
    "chunk_size_tolerance": 0.3,     # 容忍度
}
```

## 使用示例

### 测试真实文件
```bash
# 假设您有一个风控数据文件
python test_chunking_script.py /Users/<USER>/Desktop/customer_risk_data.csv
```

### 预期输出
```
🚀 向量化分块测试脚本
📋 当前配置: {'target_chars_per_chunk': 3072, ...}

🧪 测试文件: /Users/<USER>/Desktop/customer_risk_data.csv
📊 开始处理文件...

📊 分块结果分析 - customer_risk_data.csv
============================================================
📈 基本统计:
   总块数: 45
   块大小范围: 2715 - 3066 字符
   平均块大小: 2931 字符
   每块行数范围: 6 - 7 行
   平均每块行数: 6.2 行

📊 块大小分布:
   2KB-3KB:  40 个块 (88.9%) ████████████████████
   3KB-4KB:   5 个块 (11.1%) ██

📋 行数分布:
    6 行:  40 个块 (88.9%) ████████████████████
    7 行:   5 个块 (11.1%) ██

🎯 质量评估:
   目标范围: 2048 - 5120 字符
   在范围内: 45/45 (100.0%)
   行数多样性: 2 种不同值
   处理行数: 280

📝 示例块内容 (显示前3个块):
============================================================

🔸 块 1 (ID: 0):
   行范围: 0 - 5
   行数: 6
   字符数: 2850
   内容预览 (前300字符):
   --------------------------------------------------
   ## 风控数据块 1
   
   **文件**: test_customer_risk_data
   
   **数据范围**: 第1行 - 第6行 (共280行)
   
   **本块行数**: 6
   
   **数据列**: user_id, age, income, credit_score, loan_amount, employment_years, debt_ratio, risk_category
   
   ---
   
   | user_id   |   age |   income |   credit_score |   loan_amount |   employment_years |   debt_ratio | risk_category   |
   |:----------|------:|---------:|---------------:|--------------:|-------------------:|-------------:|:----------------|
   | user_001  |    25 |    45000 |            720 |         15000 |                  3 |         0.25 | 低风险          |
   ...

✅ 测试完成! 质量评分: 100.0%
```

## 注意事项
1. 确保文件路径正确且文件存在
2. 支持拖拽文件到终端获取路径
3. 大文件可能需要较长处理时间
4. 脚本会自动处理文件编码问题

## 故障排除
- **文件不存在**: 检查文件路径是否正确
- **不支持的文件类型**: 确保是CSV或Excel文件
- **处理失败**: 检查文件格式是否正确，是否有权限访问
- **内存不足**: 对于超大文件，可能需要增加系统内存
