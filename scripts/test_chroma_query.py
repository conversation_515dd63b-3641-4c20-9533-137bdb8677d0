#!/usr/bin/env python3
"""
测试ChromaDB查询修复
验证query_texts参数是否正常工作
"""

import sys
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

try:
    import chromadb
    from services.worker_service.config import config as worker_config
    from services.worker_service.core.embeddings import WorkerBGEEmbeddingService
    from shared.utils.logger import get_logger

    HAS_DEPS = True
except ImportError as e:
    HAS_DEPS = False
    print(f"依赖库导入失败: {e}")

logger = get_logger(__name__) if HAS_DEPS else None

async def test_chroma_query():
    """测试ChromaDB查询"""
    if not HAS_DEPS:
        print("❌ 依赖库未正确导入")
        return False
    
    try:
        print("🔍 测试ChromaDB查询修复")
        
        # 连接ChromaDB
        print(f"连接ChromaDB: {worker_config.chroma_host}:{worker_config.chroma_port}")
        chroma_client = chromadb.HttpClient(
            host=worker_config.chroma_host,
            port=worker_config.chroma_port
        )
        
        # 获取所有集合
        print("获取所有集合...")
        collections = chroma_client.list_collections()
        print(f"找到 {len(collections)} 个集合")
        
        if not collections:
            print("⚠️ 没有找到任何集合，请先运行向量化步骤")
            return False
        
        # 选择第一个集合进行测试
        test_collection = collections[0]
        collection_name = test_collection.name
        print(f"测试集合: {collection_name}")
        
        # 获取集合对象
        collection = chroma_client.get_collection(name=collection_name)
        
        # 初始化BGE嵌入服务
        print("初始化BGE-M3嵌入服务...")
        try:
            embedding_service = WorkerBGEEmbeddingService(
                model_name=worker_config.model_name,
                device=worker_config.device,
                batch_size=worker_config.batch_size,
                max_length=worker_config.max_length,
                normalize_embeddings=worker_config.normalize_embeddings,
                enable_cache=False,
                auto_cleanup=worker_config.auto_cleanup,
                memory_monitor=worker_config.enable_memory_monitoring,
            )
            print("✅ BGE-M3嵌入服务初始化成功")
        except Exception as embed_error:
            print(f"❌ BGE-M3嵌入服务初始化失败: {embed_error}")
            return False

        # 测试查询
        test_query = "风险数据分析"
        print(f"执行测试查询: {test_query}")

        try:
            # 使用BGE-M3生成查询嵌入向量
            print("生成查询嵌入向量...")
            query_embedding = embedding_service.embed_query(test_query)
            print(f"查询嵌入生成成功，维度: {len(query_embedding)}")

            # 使用嵌入向量查询
            results = collection.query(
                query_embeddings=[query_embedding],
                n_results=3,
                include=["documents", "distances", "metadatas"]
            )

            print("✅ 查询成功!")

            # 显示结果
            documents = results.get("documents", [[]])[0] if results.get("documents") else []
            distances = results.get("distances", [[]])[0] if results.get("distances") else []

            print(f"返回结果数量: {len(documents)}")
            if documents:
                print(f"最高相似度: {1-min(distances):.3f}")
                print(f"第一个结果预览: {documents[0][:100]}...")

            return True

        except Exception as query_error:
            print(f"❌ 查询失败: {query_error}")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("ChromaDB查询修复测试")
    print("=" * 50)
    
    # 运行测试
    success = asyncio.run(test_chroma_query())
    
    if success:
        print("\n✅ 测试通过！ChromaDB查询修复成功")
        print("现在可以重新运行worker_step_validator.py的步骤4")
    else:
        print("\n❌ 测试失败，请检查ChromaDB服务状态")
    
    return success

if __name__ == "__main__":
    main()
