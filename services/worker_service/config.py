"""
Worker服务配置
专门处理BGE-M3向量化和风险分析任务
支持系统兼容性自动检测
"""

import os
from pathlib import Path
from typing import Optional
from pydantic_settings import BaseSettings

# 导入系统兼容性检测
from shared.system_compatibility import get_optimal_device, should_force_cpu, get_memory_strategy


class WorkerServiceConfig(BaseSettings):
    """Worker服务配置"""

    # 服务基本信息
    service_name: str = "worker-service"
    service_version: str = "1.0.0"
    log_level: str = os.getenv("LOG_LEVEL", "INFO")

    # 嵌入服务配置 - 使用独立的嵌入服务
    embedding_service_url: str = os.getenv("EMBEDDING_SERVICE_URL", "http://192.168.2.102:8004")
    embedding_service_timeout: float = float(os.getenv("EMBEDDING_SERVICE_TIMEOUT", "300.0"))
    embedding_service_max_retries: int = int(os.getenv("EMBEDDING_SERVICE_MAX_RETRIES", "3"))

    # 保留BGE-M3配置用于兼容性（已弃用，使用嵌入服务）
    model_name: str = os.getenv("MODEL_NAME", str(Path(__file__).parent.parent.parent / "models" / "bge-m3-safetensors-only"))
    model_cache_dir: Optional[str] = os.getenv("MODEL_CACHE_DIR", "./models")
    device: str = os.getenv("DEVICE", "cpu")  # 不再需要GPU，因为使用远程嵌入服务
    batch_size: int = int(os.getenv("BATCH_SIZE", "32"))
    max_length: int = int(os.getenv("MAX_LENGTH", "8192"))
    normalize_embeddings: bool = True

    # ChromaDB配置 (客户端模式，连接到独立的ChromaDB服务)
    chroma_host: str = os.getenv("CHROMA_HOST", "localhost")
    chroma_port: int = int(os.getenv("CHROMA_PORT", "8001"))
    chroma_api_version: str = os.getenv("CHROMA_API_VERSION", "v2")  # 默认v1保持兼容性
    chroma_collection_prefix: str = os.getenv("CHROMA_COLLECTION_PREFIX", "file_")

    # Redis配置由统一的redis_config管理

    # LLM配置 (用于风险分析) - 使用用户自定义的OpenAI兼容服务
    llm_provider: str = os.getenv("LLM_PROVIDER", "openai")
    deepseek_api_key: str = os.getenv("DEEPSEEK_API_KEY", "***********************************")
    deepseek_model: str = os.getenv("DEEPSEEK_MODEL", "deepseek-reasoner")
    openai_base_url: str = os.getenv("OPENAI_BASE_URL", "https://www.chataiapi.com/v1")
    openai_api_key: str = os.getenv("OPENAI_API_KEY", "sk-OIhvOGp4lKO7Dze11lGqFcSXTIi0talVCdCMfIeWmIVS6iA5")
    openai_model: str = os.getenv("OPENAI_MODEL", "gemini-2.5-pro-preview-06-05")  # 用户自定义AI服务的模型
    enable_streaming: bool = os.getenv("ENABLE_STREAMING", "true").lower() == "true"

    # 提示词配置
    prompts_dir: str = os.getenv("PROMPTS_DIR", "/Users/<USER>/VsCodeProjects/deep-risk-rag/prompts")

    # 文件处理配置
    chunk_size: int = int(os.getenv("CHUNK_SIZE", "1000"))
    chunk_overlap: int = int(os.getenv("CHUNK_OVERLAP", "200"))
    max_chunks_per_file: int = int(os.getenv("MAX_CHUNKS_PER_FILE", "100"))

    # 任务配置
    task_timeout: int = int(os.getenv("TASK_TIMEOUT", "1800"))  # 30分钟
    task_soft_timeout: int = int(os.getenv("TASK_SOFT_TIMEOUT", "1500"))  # 25分钟
    max_retries: int = int(os.getenv("MAX_RETRIES", "3"))
    retry_delay: int = int(os.getenv("RETRY_DELAY", "60"))  # 60秒

    # 内存管理配置 - 集成系统兼容性检测
    memory_strategy: str = get_memory_strategy()
    enable_memory_monitoring: bool = os.getenv("ENABLE_MEMORY_MONITORING", "true").lower() == "true"
    memory_cleanup_threshold: float = float(os.getenv("MEMORY_CLEANUP_THRESHOLD", "80.0"))  # 80%
    auto_cleanup: bool = os.getenv("AUTO_CLEANUP", "true").lower() == "true"

    @property
    def chroma_url(self) -> str:
        """ChromaDB服务URL"""
        return f"http://{self.chroma_host}:{self.chroma_port}"

    def build_chroma_api_path(self, endpoint: str) -> str:
        """构建ChromaDB API路径"""
        return f"/api/{self.chroma_api_version}/{endpoint.lstrip('/')}"

    def validate_chroma_api_version(self) -> bool:
        """验证ChromaDB API版本有效性"""
        valid_versions = ["v1", "v2"]
        is_valid = self.chroma_api_version in valid_versions
        
        if not is_valid:
            import warnings
            warnings.warn(
                f"ChromaDB API版本 '{self.chroma_api_version}' 不在支持的版本列表中: {valid_versions}。"
                f"请检查配置或更新到支持的版本。",
                UserWarning
            )
        
        return is_valid

    def get_chroma_info(self) -> dict:
        """获取ChromaDB配置信息"""
        return {
            "url": self.chroma_url,
            "host": self.chroma_host,
            "port": self.chroma_port,
            "api_version": self.chroma_api_version,
            "collection_prefix": self.chroma_collection_prefix,
            "api_version_valid": self.validate_chroma_api_version()
        }

    # Redis URL 属性已移除，请使用 shared.redis_config.get_redis_url()

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        extra = "ignore"  # 忽略额外的环境变量


# 创建全局配置实例
config = WorkerServiceConfig()

# 启动时验证配置
config.validate_chroma_api_version()
