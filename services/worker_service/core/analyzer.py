"""
风险分析器 (非流式模式)
基于原分析服务的实现，专门用于Celery异步任务
"""

import uuid
import time
import chromadb
from typing import Dict, Any, Optional, List
from datetime import datetime

from shared.utils.logger import get_logger
from shared.models.analysis_result import AnalysisResult, AnalysisStatus
from services.worker_service.config import config
from services.worker_service.core.llm_client import LLMClient
# 移除embedding service导入，使用HTTP请求方式

logger = get_logger(__name__)


class RiskAnalyzer:
    """风险分析器 (非流式模式)"""

    def __init__(self):
        self.chroma_client = chromadb.HttpClient(
            host=config.chroma_host, port=config.chroma_port
        )
        self.llm_client = LLMClient()
        # 不初始化embedding service，使用HTTP请求方式

        logger.info(
            f"风险分析器初始化完成 (非流式模式)，ChromaDB URL: {config.chroma_url}"
        )

    def _get_collection_name(self, file_code: str) -> str:
        """获取集合名称"""
        return f"{config.chroma_collection_prefix}{file_code}"

    def _get_embedding_via_http(self, text: str) -> Optional[List[float]]:
        """
        通过HTTP请求获取单个查询的嵌入向量，避免异步调用冲突
        参考vectorizer.py中的实现
        """
        try:
            import requests

            url = f"{config.embedding_service_url}/api/v1/embed/query"
            payload = {
                "text": text,
                "normalize": True
            }

            response = requests.post(
                url,
                json=payload,
                timeout=config.embedding_service_timeout
            )

            if response.status_code == 200:
                result = response.json()
                embeddings = result.get("embeddings", [])
                return embeddings[0] if embeddings else []
            else:
                logger.error(f"HTTP嵌入请求失败: {response.status_code} - {response.text}")
                return None

        except Exception as e:
            logger.error(f"HTTP嵌入请求异常: {e}")
            return None

    def _get_all_documents_by_code(self, file_code: str) -> List[Dict[str, Any]]:
        """
        获取特定文件编码的所有文档 - 参考src实现

        Args:
            file_code: 文件编码

        Returns:
            所有文档列表
        """
        try:
            collection_name = self._get_collection_name(file_code)

            # 获取集合
            collection = self.chroma_client.get_collection(name=collection_name)

            # 使用HTTP请求生成空查询向量获取所有文档
            try:
                # 使用HTTP请求生成空查询的嵌入向量
                empty_query_embedding = self._get_embedding_via_http("")
                if empty_query_embedding:
                    logger.debug(f"空查询嵌入生成成功，维度: {len(empty_query_embedding)}")

                    results = collection.query(
                        query_embeddings=[empty_query_embedding],  # 使用BGE嵌入向量
                        n_results=1000,  # 假设不超过1000个文档
                        include=["documents", "metadatas", "distances"],
                    )
                else:
                    raise Exception("HTTP嵌入请求失败")
            except Exception as embed_error:
                logger.error(f"生成查询嵌入失败: {embed_error}")
                # 降级方案：直接获取集合中的所有文档
                try:
                    results = collection.get(
                        include=["documents", "metadatas"]
                    )
                    # 转换格式以兼容query结果
                    if "documents" in results:
                        results = {
                            "documents": [results["documents"]],
                            "metadatas": [results.get("metadatas", [])],
                            "distances": [[0.0] * len(results["documents"])]  # 设置默认距离
                        }
                except Exception as get_error:
                    logger.error(f"获取集合文档失败: {get_error}")
                    raise

            # 解析查询结果
            documents = results.get("documents", [[]])[0]
            metadatas = results.get("metadatas", [[]])[0]
            distances = results.get("distances", [[]])[0]

            all_documents = []
            for i in range(len(documents)):
                all_documents.append(
                    {
                        "content": documents[i],
                        "metadata": metadatas[i] if i < len(metadatas) else {},
                        "distance": distances[i] if i < len(distances) else 0.0,
                        "similarity": 1.0
                        - (distances[i] if i < len(distances) else 0.0),
                    }
                )

            logger.info(
                f"获取文件编码 {file_code} 的所有文档，数量: {len(all_documents)}"
            )
            return all_documents

        except Exception as e:
            logger.error(f"获取所有文档失败: {e}")
            return []

    def _load_prompt_template(self, analysis_type: str = "default") -> str:
        """
        加载提示词模板
        """
        try:
            # 根据分析类型选择提示词文件 - 使用实际存在的文件名
            prompt_file_map = {
                "default": "risk_analysis/default.md",
                "quick": "risk_analysis/simple.md",  # 使用simple.md作为快速分析
                "detailed": "risk_analysis/origin.md",  # 使用origin.md作为详细分析
            }

            prompt_file = prompt_file_map.get(analysis_type, prompt_file_map["default"])
            prompt_path = f"{config.prompts_dir}/{prompt_file}"

            try:
                with open(prompt_path, "r", encoding="utf-8") as f:
                    template = f.read()
                logger.debug(f"加载提示词模板: {prompt_file}")
                return template
            except FileNotFoundError:
                logger.warning(f"提示词文件不存在: {prompt_path}, 使用默认模板")
                return self._get_default_prompt_template()

        except Exception as e:
            logger.error(f"加载提示词模板失败: {e}")
            return self._get_default_prompt_template()

    def _get_default_prompt_template(self) -> str:
        """获取默认提示词模板"""
        return """
# 风险分析任务

你是一个专业的风险分析师，请基于以下数据进行风险评估。

## 分析数据
{context}

## 分析要求
1. 分析客户的违约概率
2. 识别主要风险因素
3. 提供风险等级评估
4. 给出具体的分析理由

## 输出格式
请以JSON格式返回分析结果：
```json
{{
    "default_probability": 0.0,
    "risk_level": "低风险|中风险|高风险",
    "risk_factors": ["因素1", "因素2"],
    "analysis_summary": "详细分析说明",
    "recommendations": ["建议1", "建议2"]
}}
```
"""

    def _select_important_documents(
        self, documents: List[Dict[str, Any]], max_length: int
    ) -> List[Dict[str, Any]]:
        """
        智能选择重要文档，最大化数据利用率 - 参考src实现

        Args:
            documents: 所有文档
            max_length: 最大总长度

        Returns:
            选择的重要文档列表
        """
        # 策略1: 优先选择包含关键风控指标的文档
        risk_keywords = [
            "收入",
            "负债",
            "信用",
            "逾期",
            "还款",
            "征信",
            "资产",
            "流水",
            "工资",
            "房贷",
            "车贷",
            "信用卡",
            "借款",
            "担保",
            "抵押",
            "年龄",
            "学历",
            "职业",
            "工作",
            "婚姻",
            "户籍",
            "风险",
            "压力",
            "极高",
        ]

        # 计算每个文档的重要性得分
        doc_scores = []
        for doc in documents:
            content = doc.get("content", "").lower()
            score = 0

            # 基于关键词的得分
            for keyword in risk_keywords:
                score += content.count(keyword) * 2

            # 基于相似度的得分
            similarity = doc.get("similarity", 0)
            score += similarity * 10

            # 基于文档长度的得分（适中长度更好）
            length = len(doc.get("content", ""))
            if 500 <= length <= 3000:  # 理想长度范围
                score += 5
            elif length > 3000:
                score += 2

            # 基于数字密度的得分（风控数据通常包含大量数字）
            import re

            numbers = re.findall(r"\d+", content)
            score += len(numbers) * 0.1

            doc_scores.append((score, doc, length))

        # 按得分排序
        doc_scores.sort(key=lambda x: x[0], reverse=True)

        # 贪心选择：在长度限制内选择尽可能多的高分文档
        selected_docs = []
        current_length = 0

        for score, doc, length in doc_scores:
            # 估算格式化后的长度（添加一些开销）
            estimated_length = length + 200  # 格式化开销

            if current_length + estimated_length <= max_length:
                selected_docs.append(doc)
                current_length += estimated_length
            else:
                # 尝试压缩内容以包含更多文档
                remaining_space = max_length - current_length
                if remaining_space > 500:  # 至少要有500字符才值得压缩
                    compressed_doc = self._compress_document(doc, remaining_space - 200)
                    if compressed_doc:
                        selected_docs.append(compressed_doc)
                        break

        logger.info(
            f"智能选择了 {len(selected_docs)}/{len(documents)} 个文档，预计长度: {current_length}"
        )
        return selected_docs

    def _compress_document(
        self, doc: Dict[str, Any], max_length: int
    ) -> Optional[Dict[str, Any]]:
        """
        压缩文档内容，保留关键信息

        Args:
            doc: 原始文档
            max_length: 最大长度

        Returns:
            压缩后的文档或None
        """
        content = doc.get("content", "")
        if len(content) <= max_length:
            return doc

        # 简单的压缩策略：保留前半部分和后半部分
        half_length = (max_length - 50) // 2  # 留50字符给省略号
        compressed_content = (
            content[:half_length]
            + "\n...(中间内容已省略)...\n"
            + content[-half_length:]
        )

        # 创建压缩后的文档
        compressed_doc = doc.copy()
        compressed_doc["content"] = compressed_content
        compressed_doc["metadata"] = doc.get("metadata", {}).copy()
        compressed_doc["metadata"]["compressed"] = True

        return compressed_doc

    def _format_context(self, documents: List[Dict[str, Any]]) -> str:
        """格式化上下文 - 增强版本，包含更多元数据信息"""
        if not documents:
            return "没有找到相关数据"

        context_parts = []
        for i, doc in enumerate(documents, 1):
            # 获取文档元数据
            metadata = doc.get("metadata", {})
            file_name = metadata.get("file_name", "未知文件")
            chunk_id = metadata.get("chunk_id", "")
            sheet_name = metadata.get("sheet_name", metadata.get("original_sheet", ""))

            content = doc.get("content", "").strip()

            # 格式化文档内容，包含更多元数据信息
            chunk_info = f" (块 {chunk_id})" if chunk_id != "" else ""
            sheet_info = f" - {sheet_name}" if sheet_name else ""
            compressed_info = " [已压缩]" if metadata.get("compressed") else ""

            data_part = f"""数据源 {i}{chunk_info}{sheet_info}{compressed_info}:
文件: {file_name}
内容: {content}
---"""

            context_parts.append(data_part)

        result = "\n".join(context_parts)
        logger.info(
            f"格式化上下文完成，总长度: {len(result)} 字符，文档数: {len(documents)}"
        )
        return result

    async def analyze_risk(
        self,
        file_code: str,
        analysis_type: str = "default",
        options: Optional[Dict[str, Any]] = None,
    ) -> AnalysisResult:
        """
        执行风险分析 (非流式模式)
        """
        start_time = time.time()
        analysis_id = str(uuid.uuid4())

        try:
            logger.info(f"开始风险分析: {file_code}, 类型: {analysis_type}")

            # 1. 获取该客户的所有文档 - 正确的做法：一个file_code代表一个客户的完整数据
            documents = self._get_all_documents_by_code(file_code)

            if not documents:
                raise ValueError(f"未找到文件 {file_code} 的相关数据")

            logger.info(f"获取到客户 {file_code} 的所有文档: {len(documents)} 个")

            # 2. 智能选择重要文档 - 根据分析类型调整策略
            if analysis_type == "quick":
                max_total_length = 80000  # 快速分析使用较少上下文
            elif analysis_type == "detailed":
                max_total_length = 160000  # 详细分析使用最大上下文
            else:  # default
                max_total_length = 160000  # 默认风险分析使用最大上下文

            selected_documents = self._select_important_documents(
                documents, max_total_length
            )

            # 3. 准备分析上下文
            context = self._format_context(selected_documents)
            prompt_template = self._load_prompt_template(analysis_type)
            prompt = prompt_template.format(personal_credit_data=context)
            logger.info(f"生成的分析提示词: {prompt}")
            # 4. 调用LLM进行分析
            logger.info(f"调用LLM进行分析: {config.llm_provider}")
            llm_response = await self.llm_client.analyze(
                prompt=prompt,
                analysis_type=analysis_type,
                streaming=False,  # 非流式模式
            )

            # 5. 解析分析结果
            analysis_result = self._parse_analysis_result(
                llm_response, analysis_id, file_code, start_time
            )

            logger.info(
                f"风险分析完成: {file_code}, 耗时: {analysis_result.processing_time:.2f}s"
            )
            return analysis_result

        except Exception as e:
            logger.error(f"风险分析失败: {e}")

            # 返回失败结果
            return AnalysisResult(
                analysis_id=analysis_id,
                file_code=file_code,
                status=AnalysisStatus.FAILED,
                error_message=str(e),
                processing_time=time.time() - start_time,
                created_at=datetime.now(),
                updated_at=datetime.now(),
            )

    def _parse_analysis_result(
        self, llm_response: str, analysis_id: str, file_code: str, start_time: float
    ) -> AnalysisResult:
        """解析LLM分析结果"""
        try:
            import json

            # 尝试从响应中提取JSON
            json_start = llm_response.find("{")
            json_end = llm_response.rfind("}") + 1

            if json_start >= 0 and json_end > json_start:
                json_str = llm_response[json_start:json_end]
                result_data = json.loads(json_str)

                return AnalysisResult(
                    analysis_id=analysis_id,
                    file_code=file_code,
                    status=AnalysisStatus.COMPLETED,
                    default_probability=result_data.get("default_probability", 0.0),
                    risk_level=result_data.get("risk_level", "未知"),
                    risk_factors=result_data.get("risk_factors", []),
                    analysis_summary=result_data.get("analysis_summary", ""),
                    recommendations=result_data.get("recommendations", []),
                    raw_response=llm_response,
                    processing_time=time.time() - start_time,
                    created_at=datetime.now(),
                    updated_at=datetime.now(),
                )
            else:
                # 无法解析JSON，返回原始响应
                return AnalysisResult(
                    analysis_id=analysis_id,
                    file_code=file_code,
                    status=AnalysisStatus.COMPLETED,
                    analysis_summary=llm_response,
                    raw_response=llm_response,
                    processing_time=time.time() - start_time,
                    created_at=datetime.now(),
                    updated_at=datetime.now(),
                )

        except Exception as e:
            logger.error(f"解析分析结果失败: {e}")

            return AnalysisResult(
                analysis_id=analysis_id,
                file_code=file_code,
                status=AnalysisStatus.FAILED,
                error_message=f"结果解析失败: {str(e)}",
                raw_response=llm_response,
                processing_time=time.time() - start_time,
                created_at=datetime.now(),
                updated_at=datetime.now(),
            )

    async def health_check(self) -> Dict[str, bool]:
        """健康检查"""
        logger.info("开始风险分析器健康检查...")
        checks = {}

        # 检查ChromaDB连接
        logger.info("检查ChromaDB连接...")
        try:
            # 使用原生客户端测试连接
            version_info = self.chroma_client.get_version()
            chroma_healthy = version_info is not None
            checks["chromadb"] = chroma_healthy
            if chroma_healthy:
                logger.info(f"✅ ChromaDB连接正常: {config.chroma_url}")
            else:
                logger.warning(f"❌ ChromaDB连接失败")
        except Exception as e:
            logger.error(f"❌ ChromaDB连接异常: {e}")
            checks["chromadb"] = False

        # 检查LLM服务
        logger.info("检查LLM服务...")
        try:
            llm_healthy = await self.llm_client.health_check()
            # 确保返回值是布尔类型
            llm_healthy = bool(llm_healthy) if llm_healthy is not None else False
            checks["llm_service"] = llm_healthy
            logger.info(
                f"LLM健康检查原始结果: {repr(llm_healthy)}, 类型: {type(llm_healthy)}"
            )
            if llm_healthy:
                logger.info("✅ LLM服务正常")
            else:
                logger.warning("❌ LLM服务不健康")
        except Exception as e:
            logger.error(f"❌ LLM服务检查异常: {e}")
            checks["llm_service"] = False

        logger.info(f"风险分析器健康检查完成: {checks}")
        return checks

    async def cleanup(self):
        """清理资源"""
        # ChromaDB Python客户端不需要显式关闭
        await self.llm_client.cleanup()
