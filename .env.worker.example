# Worker Service 配置示例
# 复制此文件为 .env 并根据需要修改配置

# ===== LLM 配置 =====
# LLM 提供商选择 (openai, deepseek)
LLM_PROVIDER=openai

# DeepSeek 配置
DEEPSEEK_API_KEY=your-deepseek-api-key
DEEPSEEK_MODEL=deepseek-reasoner

# OpenAI 兼容配置 (用于自定义AI服务)
OPENAI_BASE_URL=https://www.chataiapi.com/v1
OPENAI_API_KEY=your-openai-api-key
OPENAI_MODEL=gemini-2.5-pro-preview-06-05

# ===== LLM 生成参数配置 =====
# Max Tokens 配置 (0 表示不限制，适用于长上下文模型如 Gemini)
LLM_MAX_TOKENS_QUICK=0          # 快速分析的最大token数
LLM_MAX_TOKENS_DEFAULT=0        # 默认分析的最大token数  
LLM_MAX_TOKENS_DETAILED=0       # 详细分析的最大token数

# 如果需要限制token数量，可以设置具体值，例如：
# LLM_MAX_TOKENS_QUICK=2000
# LLM_MAX_TOKENS_DEFAULT=4000
# LLM_MAX_TOKENS_DETAILED=8000

# Temperature 配置 (控制生成的随机性，0.0-1.0)
LLM_TEMPERATURE_QUICK=0.1       # 快速分析的温度
LLM_TEMPERATURE_DEFAULT=0.1     # 默认分析的温度
LLM_TEMPERATURE_DETAILED=0.2    # 详细分析的温度

# ===== 其他配置 =====
# 流式输出配置
ENABLE_STREAMING=true

# 提示词目录
PROMPTS_DIR=/path/to/your/prompts

# ChromaDB 配置
CHROMA_HOST=localhost
CHROMA_PORT=8000

# Redis 配置
REDIS_HOST=localhost
REDIS_PORT=6379

# 嵌入服务配置
EMBEDDING_SERVICE_URL=http://localhost:8001
EMBEDDING_SERVICE_TIMEOUT=30
