# GPU显存自动清理功能说明

## 🎯 功能概述

为了解决GPU环境下向量化处理后显存没有释放的问题，我们实施了增强的GPU显存自动清理机制。该机制确保在每次向量化处理完成后自动释放GPU显存，同时保持模型常驻内存以维持响应速度。

## ✨ 主要改进

### 1. 增强的显存清理机制
- **每次处理后自动清理**: 在`embed_documents`完成后立即清理GPU显存
- **批次级清理**: 每个批次处理后立即清理当前批次的GPU显存
- **更频繁的周期清理**: 从每5个批次改为每2个批次进行一次深度清理
- **保持模型加载**: 只清理中间计算张量，不卸载模型

### 2. 新增专用清理方法
```python
def cleanup_gpu_memory_only(self):
    """仅清理GPU显存，不清理模型引用"""
```

### 3. 增强的清理策略
- GPU操作同步: `torch.cuda.synchronize()`
- 缓存清理: `torch.cuda.empty_cache()`
- 峰值内存重置: `torch.cuda.reset_max_memory_allocated()`
- 垃圾回收: `gc.collect()`

### 4. 新增API接口
```
POST /api/v1/embed/memory/cleanup
POST /api/v1/embed/memory/cleanup?force=true
```

## 🔧 技术实现

### 核心文件修改

#### 1. `services/embedding_service/core/bge_embedding.py`
- 增强`cleanup_memory`方法
- 新增`cleanup_gpu_memory_only`方法
- 修改`embed_documents`方法，添加自动清理
- 优化批处理清理策略

#### 2. `services/embedding_service/core/embedding_manager.py`
- 在嵌入管理器层面确保显存清理
- 异步执行清理操作，避免阻塞

#### 3. `services/embedding_service/api/embed.py`
- 新增显存清理API端点
- 支持强制清理和常规清理两种模式

## 📊 清理时机

### 自动清理触发点
1. **每个批次后**: 立即清理当前批次的GPU显存
2. **每2个批次**: 执行深度显存清理
3. **每次嵌入完成后**: 执行完整的显存清理
4. **发生错误时**: 执行显存清理防止泄漏

### 手动清理
- API接口: `POST /api/v1/embed/memory/cleanup`
- 直接调用: `embedding_service.cleanup_gpu_memory_only()`

## 🧪 测试验证

### 运行测试脚本
```bash
python test_gpu_memory_cleanup.py
```

### 测试内容
1. **自动显存清理**: 验证每次处理后显存是否正确释放
2. **多轮处理测试**: 检查是否存在显存累积泄漏
3. **手动清理测试**: 验证手动清理API的效果
4. **显存监控**: 实时监控显存使用变化

## 📈 预期效果

### 显存使用模式
- **处理前**: 基线显存使用（仅模型占用）
- **处理中**: 显存使用上升（模型 + 计算张量）
- **处理后**: 显存回到基线水平（仅模型占用）

### 性能影响
- ✅ **模型保持加载**: 响应速度不受影响
- ✅ **显存及时释放**: 避免显存累积和OOM错误
- ✅ **清理开销最小**: 清理操作耗时 < 50ms

## 🔍 监控和调试

### 显存监控日志
启用`memory_monitor=True`后，系统会记录详细的显存使用日志：
```
🔍 GPU内存使用 [嵌入完成]: 已分配=2.34GB, 已保留=2.50GB
✅ GPU显存专项清理完成
```

### API监控
通过API获取实时显存状态：
```bash
curl -X GET "http://localhost:8004/api/v1/embed/model/info"
```

### 手动清理
```bash
curl -X POST "http://localhost:8004/api/v1/embed/memory/cleanup"
```

## ⚙️ 配置选项

### 环境变量
```bash
# 启用自动清理（默认: true）
AUTO_CLEANUP=true

# 启用显存监控（默认: true）
ENABLE_MEMORY_MONITORING=true

# 显存清理阈值（默认: 0.8）
MEMORY_CLEANUP_THRESHOLD=0.8
```

### 代码配置
```python
embedding_service = BGEEmbeddingService(
    auto_cleanup=True,          # 启用自动清理
    memory_monitor=True,        # 启用显存监控
    batch_size=32              # 批次大小
)
```

## 🚨 注意事项

1. **模型保持加载**: 清理操作不会卸载模型，确保响应速度
2. **清理延迟**: 清理操作有50-100ms的延迟确保GPU操作完成
3. **错误处理**: 清理失败不会影响主要功能，只会记录警告日志
4. **兼容性**: 在CPU模式下自动降级为内存清理

## 🔧 故障排除

### 显存仍未释放
1. 检查`auto_cleanup`是否启用
2. 查看日志中的清理记录
3. 手动调用清理API测试
4. 检查是否有其他进程占用GPU

### 性能影响
1. 调整批次大小减少清理频率
2. 关闭显存监控减少日志开销
3. 调整清理间隔参数

### API调用失败
1. 确认服务正常运行
2. 检查API路径是否正确
3. 查看服务日志获取详细错误信息
